<?php

class User {
    private $id;
    private $name;
    private $email;

    public function __construct($id, $name, $email) {
        $this->id = $id;
        $this->name = $name;
        $this->email = $email;
    }

    public function getId() {
        return $this->id;
    }

    public function getName() {
        return $this->name;
    }

    public function getEmail() {
        return $this->email;
    }

    public function setName($name) {
        $this->name = $name;
    }

    public function setEmail($email) {
        $this->email = $email;
    }

    public function save() {
        // Code pour sauvegarder l'utilisateur dans la base de données
    }

    public function delete() {
        // Code pour supprimer l'utilisateur de la base de données
    }

    public static function find($id) {
        // Code pour trouver un utilisateur par ID dans la base de données
    }

    public static function all() {
        // Code pour récupérer tous les utilisateurs de la base de données
    }
}