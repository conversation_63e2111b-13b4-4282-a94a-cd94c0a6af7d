<?php
require_once '../config/database.php';

// Initialisation de l'application
session_start();

// Gestion des requêtes entrantes
$requestUri = $_SERVER['REQUEST_URI'];

// Exemple de routage simple
if ($requestUri === '/admin') {
    require_once '../src/Controllers/AdminController.php';
    $controller = new AdminController();
    $controller->index();
} elseif ($requestUri === '/login') {
    require_once '../src/Controllers/AuthController.php';
    $controller = new AuthController();
    $controller->login();
} elseif ($requestUri === '/register') {
    require_once '../src/Controllers/AuthController.php';
    $controller = new AuthController();
    $controller->register();
} else {
    // Page d'accueil par défaut
    require_once '../src/Views/layouts/main.php';
}
?>