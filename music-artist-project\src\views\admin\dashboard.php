<?php
// dashboard.php

session_start();

// Vérifiez si l'utilisateur est connecté en tant qu'administrateur
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// Inclure les fichiers nécessaires
require_once '../../controllers/AdminController.php';
require_once '../../controllers/VideoController.php';

$adminController = new AdminController();
$videoController = new VideoController();

// Récupérer les vidéos et les utilisateurs
$videos = $videoController->getAllVideos();
$users = $adminController->getAllUsers();

?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../public/css/style.css">
    <title>Tableau de bord Administrateur</title>
</head>
<body>
    <header>
        <h1>Tableau de bord Administrateur</h1>
        <nav>
            <a href="logout.php">Déconnexion</a>
        </nav>
    </header>
    <main>
        <section>
            <h2>Gestion des Vidéos</h2>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Titre</th>
                        <th>URL</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($videos as $video): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($video['id']); ?></td>
                            <td><?php echo htmlspecialchars($video['title']); ?></td>
                            <td><?php echo htmlspecialchars($video['url']); ?></td>
                            <td>
                                <a href="edit_video.php?id=<?php echo $video['id']; ?>">Modifier</a>
                                <a href="delete_video.php?id=<?php echo $video['id']; ?>">Supprimer</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </section>
        <section>
            <h2>Gestion des Utilisateurs</h2>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Email</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($user['id']); ?></td>
                            <td><?php echo htmlspecialchars($user['name']); ?></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td>
                                <a href="edit_user.php?id=<?php echo $user['id']; ?>">Modifier</a>
                                <a href="delete_user.php?id=<?php echo $user['id']; ?>">Supprimer</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </section>
    </main>
    <footer>
        <p>&copy; <?php echo date("Y"); ?> Projet Musicien. Tous droits réservés.</p>
    </footer>
</body>
</html>