<?php

class Video {
    private $id;
    private $title;
    private $url;

    public function __construct($id, $title, $url) {
        $this->id = $id;
        $this->title = $title;
        $this->url = $url;
    }

    public function getId() {
        return $this->id;
    }

    public function getTitle() {
        return $this->title;
    }

    public function getUrl() {
        return $this->url;
    }

    public function setTitle($title) {
        $this->title = $title;
    }

    public function setUrl($url) {
        $this->url = $url;
    }

    public function save() {
        // Code pour sauvegarder la vidéo dans la base de données
    }

    public function delete() {
        // Code pour supprimer la vidéo de la base de données
    }

    public static function find($id) {
        // Code pour trouver une vidéo par son ID dans la base de données
    }

    public static function all() {
        // Code pour récupérer toutes les vidéos de la base de données
    }
}