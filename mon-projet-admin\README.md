# Mon Projet Admin

Ce projet est un tableau de bord d'administration développé en PHP. Il inclut une fonctionnalité d'authentification pour gérer les utilisateurs et leurs accès.

## Structure du projet

- **config/database.php** : Configuration de la base de données.
- **public/index.php** : Point d'entrée de l'application.
- **src/Controllers/** : Contient les contrôleurs pour gérer la logique de l'application.
  - **AdminController.php** : Gère les actions liées à l'administration.
  - **AuthController.php** : Gère l'authentification des utilisateurs.
  - **DashboardController.php** : Gère l'affichage des données du tableau de bord.
- **src/Models/** : Contient les modèles de données.
  - **User.php** : Représente un utilisateur dans le système.
- **src/Views/** : Contient les vues de l'application.
  - **admin/** : Vues pour l'administration.
    - **dashboard.php** : Affiche le tableau de bord de l'administrateur.
    - **index.php** : Page d'accueil de l'administration.
  - **auth/** : Vues pour l'authentification.
    - **login.php** : Page de connexion des utilisateurs.
    - **register.php** : Page d'inscription des utilisateurs.
  - **layouts/** : Contient la structure de base du layout.
    - **main.php** : Layout principal utilisé par les autres vues.
- **.htaccess** : Configuration du serveur web.
- **composer.json** : Gestion des dépendances du projet.

## Installation

1. Clonez le dépôt.
2. Exécutez `composer install` pour installer les dépendances.
3. Configurez votre base de données dans `config/database.php`.
4. Accédez à `public/index.php` pour démarrer l'application.

## Contribuer

Les contributions sont les bienvenues ! Veuillez soumettre une demande de tirage pour toute amélioration ou correction.