<?php

class HomeController {
    public function index() {
        // Charger les vidéos et le texte modifiable pour la page d'accueil
        $videos = $this->loadVideos();
        $textContent = $this->loadTextContent();

        // Inclure la vue de la page d'accueil
        include '../views/home/<USER>';
    }

    private function loadVideos() {
        // Logique pour charger les vidéos depuis la base de données
        // Retourner un tableau de vidéos
    }

    private function loadTextContent() {
        // Logique pour charger le texte modifiable depuis la base de données
        // Retourner le contenu texte
    }
}