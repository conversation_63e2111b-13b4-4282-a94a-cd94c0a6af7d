# Projet Espace Artistes Musiciens

Ce projet est une plateforme dédiée aux artistes musiciens, permettant d'afficher des vidéos et du texte modifiable depuis un tableau de bord administrateur.

## Structure du projet

- **src/controllers** : Contient les contrôleurs pour gérer les différentes actions de l'application.
- **src/models** : Contient les modèles représentant les utilisateurs et les vidéos.
- **src/views** : Contient les vues pour l'affichage des pages.
- **public** : Contient les fichiers accessibles au public, comme les fichiers CSS et JavaScript.
- **config** : Contient la configuration de la base de données.
- **composer.json** : Fichier de configuration pour Composer.

## Installation

1. C<PERSON>z le dépôt.
2. Exécutez `composer install` pour installer les dépendances.
3. Configurez votre base de données dans `config/database.php`.
4. Accédez à `public/index.php` pour démarrer l'application.

## Contribuer

Les contributions sont les bienvenues ! Veuillez soumettre une demande de tirage pour toute amélioration ou correction.