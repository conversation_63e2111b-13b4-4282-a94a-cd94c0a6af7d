<?php
// dashboard.php

session_start();

// Vérifiez si l'utilisateur est authentifié
if (!isset($_SESSION['user_id'])) {
    header('Location: /mon-projet-admin/public/index.php');
    exit;
}

// Inclure le fichier de modèle User pour récupérer les informations de l'utilisateur
require_once __DIR__ . '/../../Models/User.php';

$user = new User();
$userInfo = $user->getUserById($_SESSION['user_id']);

?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - Administration</title>
    <link rel="stylesheet" href="/mon-projet-admin/public/css/style.css">
</head>
<body>
    <header>
        <h1>Bienvenue sur le tableau de bord, <?php echo htmlspecialchars($userInfo['name']); ?>!</h1>
        <nav>
            <ul>
                <li><a href="/mon-projet-admin/src/Views/admin/index.php">Accueil</a></li>
                <li><a href="/mon-projet-admin/public/index.php?logout=true">Déconnexion</a></li>
            </ul>
        </nav>
    </header>
    <main>
        <h2>Statistiques</h2>
        <p>Voici les statistiques de votre application.</p>
        <!-- Ajoutez ici d'autres éléments du tableau de bord -->
    </main>
    <footer>
        <p>&copy; <?php echo date("Y"); ?> Mon Projet Admin</p>
    </footer>
</body>
</html>