<?php

class VideoController {
    private $videoModel;

    public function __construct($videoModel) {
        $this->videoModel = $videoModel;
    }

    public function addVideo($title, $url) {
        // Code pour ajouter une vidéo
        return $this->videoModel->create($title, $url);
    }

    public function deleteVideo($id) {
        // Code pour supprimer une vidéo
        return $this->videoModel->delete($id);
    }

    public function updateVideo($id, $title, $url) {
        // Code pour modifier une vidéo
        return $this->videoModel->update($id, $title, $url);
    }

    public function getAllVideos() {
        // Code pour récupérer toutes les vidéos
        return $this->videoModel->getAll();
    }
}