<?php

namespace App\Controllers;

use App\Models\User;

class DashboardController
{
    public function index()
    {
        // Vérifiez si l'utilisateur est authentifié
        if (!isset($_SESSION['user_id'])) {
            header('Location: /auth/login');
            exit;
        }

        // Récupérer les données nécessaires pour le tableau de bord
        $user = User::find($_SESSION['user_id']);
        
        // Inclure la vue du tableau de bord
        include '../src/Views/admin/dashboard.php';
    }
}