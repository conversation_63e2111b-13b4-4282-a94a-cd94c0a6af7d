<?php

class User {
    private $id;
    private $username;
    private $password;
    private $email;

    public function __construct($username, $password, $email) {
        $this->username = $username;
        $this->password = password_hash($password, PASSWORD_DEFAULT);
        $this->email = $email;
    }

    public function getId() {
        return $this->id;
    }

    public function getUsername() {
        return $this->username;
    }

    public function getEmail() {
        return $this->email;
    }

    public function verifyPassword($password) {
        return password_verify($password, $this->password);
    }

    public function save() {
        // Code pour enregistrer l'utilisateur dans la base de données
    }

    public static function findById($id) {
        // Code pour trouver un utilisateur par ID dans la base de données
    }

    public static function findByUsername($username) {
        // Code pour trouver un utilisateur par nom d'utilisateur dans la base de données
    }
}